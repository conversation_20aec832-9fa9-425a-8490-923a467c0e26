from typing import Sequence, <PERSON><PERSON>, <PERSON>, overload
from typing_extensions import Literal
from pygame.cursors import <PERSON>ursor
from pygame.surface import Surface

@overload
def get_pressed(num_buttons: Literal[3] = 3) -> Tuple[bool, bool, bool]: ...
@overload
def get_pressed(num_buttons: Literal[5]) -> Tuple[bool, bool, bool, bool, bool]: ...
def get_pos() -> <PERSON>ple[int, int]: ...
def get_rel() -> Tuple[int, int]: ...
@overload
def set_pos(pos: Union[Sequence[float], Tuple[float, float]]) -> None: ...
@overload
def set_pos(x: float, y: float) -> None: ...
def set_visible(value: bool | Literal[0] | Literal[1]) -> int: ...
def get_visible() -> bool: ...
def get_focused() -> bool: ...
@overload
def set_cursor(cursor: Cursor) -> None: ...
@overload
def set_cursor(constant: int) -> None: ...
@overload
def set_cursor(
    size: Union[Tuple[int, int], Sequence[int]],
    hotspot: Union[Tuple[int, int], Sequence[int]],
    xormasks: Sequence[int],
    andmasks: Sequence[int],
) -> None: ...
@overload
def set_cursor(
    hotspot: Union[Tuple[int, int], Sequence[int]], surface: Surface
) -> None: ...
def get_cursor() -> Cursor: ...
def set_system_cursor(cursor: int) -> None: ...
