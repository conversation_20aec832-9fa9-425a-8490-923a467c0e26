<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.sndarray &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.sprite" href="sprite.html" />
    <link rel="prev" title="pygame.sdl2_video" href="sdl2_video.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.sndarray">
<span id="pygame-sndarray"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.sndarray</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame module for accessing sound sample data</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sndarray.html#pygame.sndarray.array">pygame.sndarray.array</a></div>
</td>
<td>—</td>
<td>copy Sound samples into an array</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sndarray.html#pygame.sndarray.samples">pygame.sndarray.samples</a></div>
</td>
<td>—</td>
<td>reference Sound samples into an array</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sndarray.html#pygame.sndarray.make_sound">pygame.sndarray.make_sound</a></div>
</td>
<td>—</td>
<td>convert an array into a Sound object</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sndarray.html#pygame.sndarray.use_arraytype">pygame.sndarray.use_arraytype</a></div>
</td>
<td>—</td>
<td>Sets the array system to be used for sound arrays</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sndarray.html#pygame.sndarray.get_arraytype">pygame.sndarray.get_arraytype</a></div>
</td>
<td>—</td>
<td>Gets the currently active array type.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sndarray.html#pygame.sndarray.get_arraytypes">pygame.sndarray.get_arraytypes</a></div>
</td>
<td>—</td>
<td>Gets the array system types currently supported.</td>
</tr>
</tbody>
</table>
<p>Functions to convert between NumPy arrays and Sound objects. This
module will only be functional when pygame can use the external NumPy
package. If NumPy can't be imported, <code class="docutils literal notranslate"><span class="pre">surfarray</span></code> becomes a <code class="docutils literal notranslate"><span class="pre">MissingModule</span></code>
object.</p>
<p>Sound data is made of thousands of samples per second, and each sample is the
amplitude of the wave at a particular moment in time. For example, in 22-kHz
format, element number 5 of the array is the amplitude of the wave after
5/22000 seconds.</p>
<p>The arrays are indexed by the <code class="docutils literal notranslate"><span class="pre">X</span></code> axis first, followed by the <code class="docutils literal notranslate"><span class="pre">Y</span></code> axis.
Each sample is an 8-bit or 16-bit integer, depending on the data format. A
stereo sound file has two values per sample, while a mono sound file only has
one.</p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.sndarray.array">
<span class="sig-prename descclassname"><span class="pre">pygame.sndarray.</span></span><span class="sig-name descname"><span class="pre">array</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.sndarray.array" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">copy Sound samples into an array</span></div>
<div class="line"><span class="signature">array(Sound) -&gt; array</span></div>
</div>
<p>Creates a new array for the sound data and copies the samples. The array
will always be in the format returned from <code class="docutils literal notranslate"><span class="pre">pygame.mixer.get_init()</span></code>.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.sndarray.samples">
<span class="sig-prename descclassname"><span class="pre">pygame.sndarray.</span></span><span class="sig-name descname"><span class="pre">samples</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.sndarray.samples" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">reference Sound samples into an array</span></div>
<div class="line"><span class="signature">samples(Sound) -&gt; array</span></div>
</div>
<p>Creates a new array that directly references the samples in a Sound object.
Modifying the array will change the Sound. The array will always be in the
format returned from <code class="docutils literal notranslate"><span class="pre">pygame.mixer.get_init()</span></code>.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.sndarray.make_sound">
<span class="sig-prename descclassname"><span class="pre">pygame.sndarray.</span></span><span class="sig-name descname"><span class="pre">make_sound</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.sndarray.make_sound" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">convert an array into a Sound object</span></div>
<div class="line"><span class="signature">make_sound(array) -&gt; Sound</span></div>
</div>
<p>Create a new playable Sound object from an array. The mixer module must be
initialized and the array format must be similar to the mixer audio format.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.sndarray.use_arraytype">
<span class="sig-prename descclassname"><span class="pre">pygame.sndarray.</span></span><span class="sig-name descname"><span class="pre">use_arraytype</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.sndarray.use_arraytype" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Sets the array system to be used for sound arrays</span></div>
<div class="line"><span class="signature">use_arraytype (arraytype) -&gt; None</span></div>
</div>
<p>DEPRECATED: Uses the requested array type for the module functions. The
only supported arraytype is <code class="docutils literal notranslate"><span class="pre">'numpy'</span></code>. Other values will raise ValueError.
Using this function will raise a <code class="docutils literal notranslate"><span class="pre">DeprecationWarning</span></code>.
.. ## pygame.sndarray.use_arraytype ##</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.sndarray.get_arraytype">
<span class="sig-prename descclassname"><span class="pre">pygame.sndarray.</span></span><span class="sig-name descname"><span class="pre">get_arraytype</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.sndarray.get_arraytype" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets the currently active array type.</span></div>
<div class="line"><span class="signature">get_arraytype () -&gt; str</span></div>
</div>
<p>DEPRECATED: Returns the currently active array type. This will be a value of the
<code class="docutils literal notranslate"><span class="pre">get_arraytypes()</span></code> tuple and indicates which type of array module is used
for the array creation. Using this function will raise a <code class="docutils literal notranslate"><span class="pre">DeprecationWarning</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.8.</span></p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.sndarray.get_arraytypes">
<span class="sig-prename descclassname"><span class="pre">pygame.sndarray.</span></span><span class="sig-name descname"><span class="pre">get_arraytypes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.sndarray.get_arraytypes" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets the array system types currently supported.</span></div>
<div class="line"><span class="signature">get_arraytypes () -&gt; tuple</span></div>
</div>
<p>DEPRECATED: Checks, which array systems are available and returns them as a tuple of
strings. The values of the tuple can be used directly in the
<a class="tooltip reference internal" href="#pygame.sndarray.use_arraytype" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.sndarray.use_arraytype()</span></code><span class="tooltip-content">Sets the array system to be used for sound arrays</span></a> () method. If no supported array
system could be found, None will be returned.  Using this function will raise a
<code class="docutils literal notranslate"><span class="pre">DeprecationWarning</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.8.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref/sndarray.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sprite.html" title="pygame.sprite"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="sdl2_video.html" title="pygame.sdl2_video"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.sndarray</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>