<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Pygame Tutorials - Import and Initialize &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Making Games With Pygame" href="MakeGames.html" />
    <link rel="prev" title="Pygame Tutorials - Setting Display Modes" href="DisplayModes.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="../ref/color.html">Color</a> | 
	    <a href="../ref/display.html">display</a> | 
	    <a href="../ref/draw.html">draw</a> | 
	    <a href="../ref/event.html">event</a> | 
	    <a href="../ref/font.html">font</a> | 
	    <a href="../ref/image.html">image</a> | 
	    <a href="../ref/key.html">key</a> | 
	    <a href="../ref/locals.html">locals</a> | 
	    <a href="../ref/mixer.html">mixer</a> | 
	    <a href="../ref/mouse.html">mouse</a> | 
	    <a href="../ref/rect.html">Rect</a> | 
	    <a href="../ref/surface.html">Surface</a> | 
	    <a href="../ref/time.html">time</a> | 
	    <a href="../ref/music.html">music</a> | 
	    <a href="../ref/pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="../ref/cursors.html">cursors</a> | 
	    <a href="../ref/joystick.html">joystick</a> | 
	    <a href="../ref/mask.html">mask</a> | 
	    <a href="../ref/sprite.html">sprite</a> | 
	    <a href="../ref/transform.html">transform</a> | 
	    <a href="../ref/bufferproxy.html">BufferProxy</a> | 
	    <a href="../ref/freetype.html">freetype</a> | 
	    <a href="../ref/gfxdraw.html">gfxdraw</a> | 
	    <a href="../ref/midi.html">midi</a> | 
	    <a href="../ref/pixelarray.html">PixelArray</a> | 
	    <a href="../ref/pixelcopy.html">pixelcopy</a> | 
	    <a href="../ref/sndarray.html">sndarray</a> | 
	    <a href="../ref/surfarray.html">surfarray</a> | 
	    <a href="../ref/math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="../ref/camera.html">camera</a> | 
	    <a href="../ref/sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="../ref/examples.html">examples</a> | 
	    <a href="../ref/fastevent.html">fastevent</a> | 
	    <a href="../ref/scrap.html">scrap</a> | 
	    <a href="../ref/tests.html">tests</a> | 
	    <a href="../ref/touch.html">touch</a> | 
	    <a href="../ref/pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="pygame-tutorials-import-and-initialize">
<section id="import-and-initialize">
<h2>Import and Initialize<a class="headerlink" href="#import-and-initialize" title="Link to this heading">¶</a></h2>
<dl class="docinfo field-list simple">
<dt class="field-odd">Author<span class="colon">:</span></dt>
<dd class="field-odd"><p>Pete Shinners</p>
</dd>
<dt class="field-even">Contact<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="mailto:pete&#37;&#52;&#48;shinners&#46;org">pete<span>&#64;</span>shinners<span>&#46;</span>org</a></p>
</dd>
</dl>
<p>Getting pygame imported and initialized is a very simple process. It is also
flexible enough to give you control over what is happening. Pygame is a
collection of different modules in a single python package. Some of the
modules are written in C, and some are written in python. Some modules
are also optional, and might not always be present.</p>
<p>This is just a quick introduction on what is going on when you import pygame.
For a clearer explanation definitely see the pygame examples.</p>
<section id="import">
<h3>Import<a class="headerlink" href="#import" title="Link to this heading">¶</a></h3>
<p>First we must import the pygame package. Since pygame version 1.4 this
has been updated to be much easier. Most games will import all of pygame like this.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pygame</span>
<span class="kn">from</span> <span class="nn">pygame.locals</span> <span class="kn">import</span> <span class="o">*</span>
</pre></div>
</div>
<p>The first line here is the only necessary one. It imports all the available pygame
modules into the pygame package. The second line is optional, and puts a limited
set of constants and functions into the global namespace of your script.</p>
<p>An important thing to keep in mind is that several pygame modules are optional.
For example, one of these is the font module. When  you &quot;import pygame&quot;, pygame
will check to see if the font module is available. If the font module is available
it will be imported as &quot;pygame.font&quot;. If the module is not available, &quot;pygame.font&quot;
will be set to None. This makes it fairly easy to later on test if the font module is available.</p>
</section>
<section id="init">
<h3>Init<a class="headerlink" href="#init" title="Link to this heading">¶</a></h3>
<p>Before you can do much with pygame, you will need to initialize it. The most common
way to do this is just make one call.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">pygame</span><span class="o">.</span><span class="n">init</span><span class="p">()</span>
</pre></div>
</div>
<p>This will attempt to initialize all the pygame modules for you. Not all pygame modules
need to be initialized, but this will automatically initialize the ones that do. You can
also easily initialize each pygame module by hand. For example to only initialize the
font module you would just call.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">pygame</span><span class="o">.</span><span class="n">font</span><span class="o">.</span><span class="n">init</span><span class="p">()</span>
</pre></div>
</div>
<p>Note that if there is an error when you initialize with &quot;pygame.init()&quot;, it will silently fail.
When hand initializing modules like this, any errors will raise an exception. Any
modules that must be initialized also have a &quot;get_init()&quot; function, which will return true
if the module has been initialized.</p>
<p>It is safe to call the init() function for any module more than once.</p>
</section>
<section id="quit">
<h3>Quit<a class="headerlink" href="#quit" title="Link to this heading">¶</a></h3>
<p>Modules that are initialized also usually have a quit() function that will clean up.
There is no need to explicitly call these, as pygame will cleanly quit all the
initialized modules when python finishes.</p>
</section>
</section>
</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/tut/ImportInit.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="MakeGames.html" title="Making Games With Pygame"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="DisplayModes.html" title="Pygame Tutorials - Setting Display Modes"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Pygame Tutorials - Import and Initialize</a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>