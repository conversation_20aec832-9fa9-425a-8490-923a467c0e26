/*
    pygame - Python Game Library
    Copyright (C) 2000-2001  <PERSON>

    This library is free software; you can redistribute it and/or
    modify it under the terms of the GNU Library General Public
    License as published by the Free Software Foundation; either
    version 2 of the License, or (at your option) any later version.

    This library is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    Library General Public License for more details.

    You should have received a copy of the GNU Library General Public
    License along with this library; if not, write to the Free
    Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA

    Pete Shinners
    <EMAIL>
*/

#ifndef PALETTE_H
#define PALETTE_H

#include <SDL.h>

/* SDL 2 does not assign a default palette color scheme to a new 8 bit
 * surface. Instead, the palette is set all white. This defines the SDL 1.2
 * default palette.
 */
static const SDL_Color default_palette_colors[] = {
    {0, 0, 0, 255},       {0, 0, 85, 255},      {0, 0, 170, 255},
    {0, 0, 255, 255},     {0, 36, 0, 255},      {0, 36, 85, 255},
    {0, 36, 170, 255},    {0, 36, 255, 255},    {0, 73, 0, 255},
    {0, 73, 85, 255},     {0, 73, 170, 255},    {0, 73, 255, 255},
    {0, 109, 0, 255},     {0, 109, 85, 255},    {0, 109, 170, 255},
    {0, 109, 255, 255},   {0, 146, 0, 255},     {0, 146, 85, 255},
    {0, 146, 170, 255},   {0, 146, 255, 255},   {0, 182, 0, 255},
    {0, 182, 85, 255},    {0, 182, 170, 255},   {0, 182, 255, 255},
    {0, 219, 0, 255},     {0, 219, 85, 255},    {0, 219, 170, 255},
    {0, 219, 255, 255},   {0, 255, 0, 255},     {0, 255, 85, 255},
    {0, 255, 170, 255},   {0, 255, 255, 255},   {85, 0, 0, 255},
    {85, 0, 85, 255},     {85, 0, 170, 255},    {85, 0, 255, 255},
    {85, 36, 0, 255},     {85, 36, 85, 255},    {85, 36, 170, 255},
    {85, 36, 255, 255},   {85, 73, 0, 255},     {85, 73, 85, 255},
    {85, 73, 170, 255},   {85, 73, 255, 255},   {85, 109, 0, 255},
    {85, 109, 85, 255},   {85, 109, 170, 255},  {85, 109, 255, 255},
    {85, 146, 0, 255},    {85, 146, 85, 255},   {85, 146, 170, 255},
    {85, 146, 255, 255},  {85, 182, 0, 255},    {85, 182, 85, 255},
    {85, 182, 170, 255},  {85, 182, 255, 255},  {85, 219, 0, 255},
    {85, 219, 85, 255},   {85, 219, 170, 255},  {85, 219, 255, 255},
    {85, 255, 0, 255},    {85, 255, 85, 255},   {85, 255, 170, 255},
    {85, 255, 255, 255},  {170, 0, 0, 255},     {170, 0, 85, 255},
    {170, 0, 170, 255},   {170, 0, 255, 255},   {170, 36, 0, 255},
    {170, 36, 85, 255},   {170, 36, 170, 255},  {170, 36, 255, 255},
    {170, 73, 0, 255},    {170, 73, 85, 255},   {170, 73, 170, 255},
    {170, 73, 255, 255},  {170, 109, 0, 255},   {170, 109, 85, 255},
    {170, 109, 170, 255}, {170, 109, 255, 255}, {170, 146, 0, 255},
    {170, 146, 85, 255},  {170, 146, 170, 255}, {170, 146, 255, 255},
    {170, 182, 0, 255},   {170, 182, 85, 255},  {170, 182, 170, 255},
    {170, 182, 255, 255}, {170, 219, 0, 255},   {170, 219, 85, 255},
    {170, 219, 170, 255}, {170, 219, 255, 255}, {170, 255, 0, 255},
    {170, 255, 85, 255},  {170, 255, 170, 255}, {170, 255, 255, 255},
    {255, 0, 0, 255},     {255, 0, 85, 255},    {255, 0, 170, 255},
    {255, 0, 255, 255},   {255, 36, 0, 255},    {255, 36, 85, 255},
    {255, 36, 170, 255},  {255, 36, 255, 255},  {255, 73, 0, 255},
    {255, 73, 85, 255},   {255, 73, 170, 255},  {255, 73, 255, 255},
    {255, 109, 0, 255},   {255, 109, 85, 255},  {255, 109, 170, 255},
    {255, 109, 255, 255}, {255, 146, 0, 255},   {255, 146, 85, 255},
    {255, 146, 170, 255}, {255, 146, 255, 255}, {255, 182, 0, 255},
    {255, 182, 85, 255},  {255, 182, 170, 255}, {255, 182, 255, 255},
    {255, 219, 0, 255},   {255, 219, 85, 255},  {255, 219, 170, 255},
    {255, 219, 255, 255}, {255, 255, 0, 255},   {255, 255, 85, 255},
    {255, 255, 170, 255}, {255, 255, 255, 255}, {0, 0, 0, 255},
    {0, 0, 85, 255},      {0, 0, 170, 255},     {0, 0, 255, 255},
    {0, 36, 0, 255},      {0, 36, 85, 255},     {0, 36, 170, 255},
    {0, 36, 255, 255},    {0, 73, 0, 255},      {0, 73, 85, 255},
    {0, 73, 170, 255},    {0, 73, 255, 255},    {0, 109, 0, 255},
    {0, 109, 85, 255},    {0, 109, 170, 255},   {0, 109, 255, 255},
    {0, 146, 0, 255},     {0, 146, 85, 255},    {0, 146, 170, 255},
    {0, 146, 255, 255},   {0, 182, 0, 255},     {0, 182, 85, 255},
    {0, 182, 170, 255},   {0, 182, 255, 255},   {0, 219, 0, 255},
    {0, 219, 85, 255},    {0, 219, 170, 255},   {0, 219, 255, 255},
    {0, 255, 0, 255},     {0, 255, 85, 255},    {0, 255, 170, 255},
    {0, 255, 255, 255},   {85, 0, 0, 255},      {85, 0, 85, 255},
    {85, 0, 170, 255},    {85, 0, 255, 255},    {85, 36, 0, 255},
    {85, 36, 85, 255},    {85, 36, 170, 255},   {85, 36, 255, 255},
    {85, 73, 0, 255},     {85, 73, 85, 255},    {85, 73, 170, 255},
    {85, 73, 255, 255},   {85, 109, 0, 255},    {85, 109, 85, 255},
    {85, 109, 170, 255},  {85, 109, 255, 255},  {85, 146, 0, 255},
    {85, 146, 85, 255},   {85, 146, 170, 255},  {85, 146, 255, 255},
    {85, 182, 0, 255},    {85, 182, 85, 255},   {85, 182, 170, 255},
    {85, 182, 255, 255},  {85, 219, 0, 255},    {85, 219, 85, 255},
    {85, 219, 170, 255},  {85, 219, 255, 255},  {85, 255, 0, 255},
    {85, 255, 85, 255},   {85, 255, 170, 255},  {85, 255, 255, 255},
    {170, 0, 0, 255},     {170, 0, 85, 255},    {170, 0, 170, 255},
    {170, 0, 255, 255},   {170, 36, 0, 255},    {170, 36, 85, 255},
    {170, 36, 170, 255},  {170, 36, 255, 255},  {170, 73, 0, 255},
    {170, 73, 85, 255},   {170, 73, 170, 255},  {170, 73, 255, 255},
    {170, 109, 0, 255},   {170, 109, 85, 255},  {170, 109, 170, 255},
    {170, 109, 255, 255}, {170, 146, 0, 255},   {170, 146, 85, 255},
    {170, 146, 170, 255}, {170, 146, 255, 255}, {170, 182, 0, 255},
    {170, 182, 85, 255},  {170, 182, 170, 255}, {170, 182, 255, 255},
    {170, 219, 0, 255},   {170, 219, 85, 255},  {170, 219, 170, 255},
    {170, 219, 255, 255}, {170, 255, 0, 255},   {170, 255, 85, 255},
    {170, 255, 170, 255}, {170, 255, 255, 255}, {255, 0, 0, 255},
    {255, 0, 85, 255},    {255, 0, 170, 255},   {255, 0, 255, 255},
    {255, 36, 0, 255},    {255, 36, 85, 255},   {255, 36, 170, 255},
    {255, 36, 255, 255},  {255, 73, 0, 255},    {255, 73, 85, 255},
    {255, 73, 170, 255},  {255, 73, 255, 255},  {255, 109, 0, 255},
    {255, 109, 85, 255},  {255, 109, 170, 255}, {255, 109, 255, 255},
    {255, 146, 0, 255},   {255, 146, 85, 255},  {255, 146, 170, 255},
    {255, 146, 255, 255}, {255, 182, 0, 255},   {255, 182, 85, 255},
    {255, 182, 170, 255}, {255, 182, 255, 255}, {255, 219, 0, 255},
    {255, 219, 85, 255},  {255, 219, 170, 255}, {255, 219, 255, 255},
    {255, 255, 0, 255},   {255, 255, 85, 255},  {255, 255, 170, 255},
    {255, 255, 255, 255}};

static const int default_palette_size =
    (int)(sizeof(default_palette_colors) / sizeof(SDL_Color));

#endif
