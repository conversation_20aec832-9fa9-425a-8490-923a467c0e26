import pygame
import random
import sys

# 初始化pygame
pygame.init()

# 設定遊戲視窗
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("飛機射擊遊戲")

# 顏色定義
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
BLACK = (0, 0, 0)
YELLOW = (255, 255, 0)

# 載入圖片
player_img = pygame.Surface((50, 40))
player_img.fill(BLUE)
enemy_img = pygame.Surface((40, 30))
enemy_img.fill(RED)
bullet_img = pygame.Surface((10, 10))
bullet_img.fill(YELLOW)
missile_img = pygame.Surface((15, 15))
missile_img.fill(GREEN)

# 玩家類
class Player:
    def __init__(self):
        self.image = player_img
        self.rect = self.image.get_rect()
        self.rect.centerx = WIDTH // 2
        self.rect.bottom = HEIGHT - 10
        self.speed = 5
        self.health = 100
        self.weapon = "bullet"  # 預設武器
        
    def update(self, keys):
        if keys[pygame.K_LEFT] and self.rect.left > 0:
            self.rect.x -= self.speed
        if keys[pygame.K_RIGHT] and self.rect.right < WIDTH:
            self.rect.x += self.speed
        if keys[pygame.K_UP] and self.rect.top > 0:
            self.rect.y -= self.speed
        if keys[pygame.K_DOWN] and self.rect.bottom < HEIGHT:
            self.rect.y += self.speed
            
    def switch_weapon(self):
        if self.weapon == "bullet":
            self.weapon = "missile"
        else:
            self.weapon = "bullet"
        
    def shoot(self):
        if self.weapon == "bullet":
            return Bullet(self.rect.centerx, self.rect.top)
        else:
            return Missile(self.rect.centerx, self.rect.top)
    
    def draw(self, surface):
        surface.blit(self.image, self.rect)
        # 繪製血量條
        pygame.draw.rect(surface, RED, (self.rect.x, self.rect.bottom + 5, self.rect.width, 5))
        pygame.draw.rect(surface, GREEN, (self.rect.x, self.rect.bottom + 5, 
                                         self.rect.width * (self.health/100), 5))

# 敵人類
class Enemy:
    def __init__(self):
        self.image = enemy_img
        self.rect = self.image.get_rect()
        self.rect.x = random.randint(0, WIDTH - self.rect.width)
        self.rect.y = random.randint(-100, -40)
        self.speed = random.randint(1, 3)
        self.detected = False
        
    def update(self):
        self.rect.y += self.speed
        if self.rect.top > HEIGHT:
            self.rect.x = random.randint(0, WIDTH - self.rect.width)
            self.rect.y = random.randint(-100, -40)
            self.speed = random.randint(1, 3)
            self.detected = False
            
    def draw(self, surface):
        surface.blit(self.image, self.rect)
        if self.detected:
            pygame.draw.circle(surface, RED, self.rect.center, 30, 1)

# 子彈類
class Bullet:
    def __init__(self, x, y):
        self.image = bullet_img
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.bottom = y
        self.speed = 7
        self.damage = 10
        
    def update(self):
        self.rect.y -= self.speed
        
    def draw(self, surface):
        surface.blit(self.image, self.rect)

# 導彈類
class Missile:
    def __init__(self, x, y):
        self.image = missile_img
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.bottom = y
        self.speed = 5
        self.damage = 25
        
    def update(self):
        self.rect.y -= self.speed
        
    def draw(self, surface):
        surface.blit(self.image, self.rect)

# 主遊戲函數
def main():
    clock = pygame.time.Clock()
    font = pygame.font.SysFont(None, 36)
    
    player = Player()
    enemies = [Enemy() for _ in range(5)]
    projectiles = []
    
    running = True
    while running:
        # 事件處理
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    projectiles.append(player.shoot())
                elif event.key == pygame.K_TAB:
                    player.switch_weapon()
        
        # 更新遊戲狀態
        keys = pygame.key.get_pressed()
        player.update(keys)
        
        for enemy in enemies:
            enemy.update()
            # 檢測敵人是否接近玩家
            if abs(enemy.rect.centerx - player.rect.centerx) < 150 and enemy.rect.y > 0:
                enemy.detected = True
            
            # 檢測碰撞
            if player.rect.colliderect(enemy.rect):
                player.health -= 5
                if player.health <= 0:
                    running = False
        
        for proj in projectiles[:]:
            proj.update()
            if proj.rect.bottom < 0:
                projectiles.remove(proj)
            else:
                for enemy in enemies[:]:
                    if proj.rect.colliderect(enemy.rect):
                        enemies.remove(enemy)
                        projectiles.remove(proj)
                        enemies.append(Enemy())
                        break
        
        # 繪製畫面
        screen.fill(BLACK)
        
        # 繪製玩家和敵人
        player.draw(screen)
        for enemy in enemies:
            enemy.draw(screen)
        
        # 繪製子彈/導彈
        for proj in projectiles:
            proj.draw(screen)
        
        # 繪製HUD
        health_text = font.render(f"血量: {player.health}", True, WHITE)
        weapon_text = font.render(f"武器: {player.weapon}", True, WHITE)
        screen.blit(health_text, (10, 10))
        screen.blit(weapon_text, (10, 50))
        
        # 敵機警告
        warning = False
        for enemy in enemies:
            if enemy.detected:
                warning = True
                break
        
        if warning:
            warning_text = font.render("警告! 敵機接近!", True, RED)
            screen.blit(warning_text, (WIDTH//2 - 100, 10))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()